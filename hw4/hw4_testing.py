from homework4 import *

# print('*'*100)
# print('2')
# b= [[False, False], [False, False]]
# g= DominoesGame(b)
# print(g.get_board())
# b= [[True, False], [True, False]]
# g= DominoesGame(b)
# print(g.get_board())
# print('*'*100)
# print('3')
# g= create_dominoes_game(2, 2)
# print(g.get_board())
# g= create_dominoes_game(2, 3)
# print(g.get_board())
# print('*'*100)
# print('4')
# b= [[False, False], [False, False]]
# g= DominoesGame(b)
# print(g.get_board())
# g.reset()
# print(g.get_board())
# b= [[True, False], [True, False]]
# g= DominoesGame(b)
# print(g.get_board())
# g.reset()
# print(g.get_board())
# print('*'*100)
# print('5')
# b= [[False, False], [False, False]]
# g= DominoesGame(b)
# print(g.is_legal_move(0, 0, True))
# print(g.is_legal_move(0, 0, False))
# b= [[True, False], [True, False]]
# g= DominoesGame(b)
# print(g.is_legal_move(0, 0, False))
# print(g.is_legal_move(0, 1, True))
# print(g.is_legal_move(1, 1, True))
# print('*'*100)
# print('6')
# g= create_dominoes_game(3, 3)
# print(list(g.legal_moves(True)))
# print(list(g.legal_moves(False)))
# print('*'*100)
# print('7')
# g= create_dominoes_game(3, 3)
# g.perform_move(0, 1, True)
# print(g.get_board())
# g= create_dominoes_game(3, 3)
# g.perform_move(1, 0, False)
# print(g.get_board())
# print('*'*100)
# print('8')
# b= [[False, False], [False, False]]
# g= DominoesGame(b)
# print(g.game_over(True))
# print(g.game_over(False))
# b= [[True, False], [True, False]]
# g= DominoesGame(b)
# print(g.game_over(True))
# print(g.game_over(False))
# print('*'*100)
# print('9')
# g= create_dominoes_game(4, 4)
# g2 = g.copy()
# print(g.get_board() == g2.get_board())
# g= create_dominoes_game(4, 4)
# g2 = g.copy()
# g.perform_move(0, 0, True)
# print(g.get_board() == g2.get_board())
# print('*'*100)
# print('10')
# b= [[False, False], [False, False]]
# g= DominoesGame(b)
# for m, new_g in g.successors(True):
#     print(m, new_g.get_board())
# b=  [[True, False], [True, False]]
# g= DominoesGame(b)
# for m, new_g in g.successors(True):
#     print(m, new_g.get_board())
print('*'*100)
print('11')
b= [[False] * 3 for i in range(3)]
g= DominoesGame(b)
print(g.get_best_move(True, 1))
print(g.get_best_move(True, 2))
print('*'*100)
b= [[False] * 3 for i in range(3)]
g= DominoesGame(b)
g.perform_move(0, 1, True)
print(g.get_best_move(False, 1))
print(g.get_best_move(False, 2))
print('*'*100)
print('*'*100)
print('*'*100)
print('*'*100)
print('*'*100)
print('*'*100)

