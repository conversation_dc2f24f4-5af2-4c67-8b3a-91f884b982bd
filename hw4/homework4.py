############################################################
# Imports
############################################################

# Include your imports here, if any are used.

import collections
import copy
import itertools
import random
import math


############################################################
# CIS 521: Homework 4
############################################################

student_name = "Valencia Richards"

############################################################
# Section 1: Dominoes Game
############################################################


def create_dominoes_game(rows, cols):
    return DominoesGame([
        [False for col in range(cols)] for row in range(rows)
        ])


class DominoesGame(object):

    # Required
    def __init__(self, board):
        self.board = list(board)
        self.row_count = len(board)
        self.col_count = len(board[0])

    def get_board(self):
        return self.board

    def reset(self):
        self.board = [
                [
                    False for col in range(self.col_count)
                ] for row in range(self.row_count)
            ]

    def is_legal_move(self, row, col, vertical):
        if vertical:
            tentative_positions = [(row, col), (row + 1, col)]
        else:
            tentative_positions = [(row, col), (row, col + 1)]
        for (tentative_row, tentative_col) in tentative_positions:
            # Out of bounds
            if tentative_row < 0 or tentative_row >= self.row_count\
                    or tentative_col < 0 or tentative_col >= self.col_count:
                return False
            # Occupied
            if self.board[tentative_row][tentative_col]:
                return False
        return True

    def legal_moves(self, vertical):
        for row in range(self.row_count):
            for col in range(self.col_count):
                if self.is_legal_move(row, col, vertical):
                    yield (row, col)

    def perform_move(self, row, col, vertical):
        self.board[row][col] = True
        if vertical:
            self.board[row + 1][col] = True
        else:
            self.board[row][col + 1] = True

    def game_over(self, vertical):
        return False if any(self.legal_moves(vertical)) else True

    def copy(self):
        new_board = list()
        for row in range(self.row_count):
            new_board.append(list())
            for col in range(self.col_count):
                new_board[row].append(self.board[row][col])
        return DominoesGame(new_board)

    def successors(self, vertical):
        for (row, col) in self.legal_moves(vertical):
            new_game = self.copy()
            new_game.perform_move(row, col, vertical)
            yield ((row, col), new_game)

    def get_random_move(self, vertical):
        return random.choice(list(self.legal_moves(vertical)))

    def get_utility(self, vertical):
        return len(self.legal_moves(vertical))\
            - len(self.legal_moves(not vertical))
    
    def max_value(self, vertical, alpha, beta, limit):
        if limit == 0 or self.game_over(vertical):
            return self.get_utility(vertical), None, 1
        v = -math.inf
        best_move = None
        num_leaves = 0
        for move, new_game in self.successors(vertical):
            v2, _, num_leaves2 = new_game.min_value(not vertical, alpha, beta, limit - 1)
            num_leaves += num_leaves2
            if v2 > v:
                v, best_move = v2, move
            alpha = max(alpha, v)
            if v >= beta:
                return v, best_move, num_leaves
        return v, best_move, num_leaves


    # Required
    def get_best_move(self, vertical, limit):
        """
        In the DominoesGame class, write a method get_best_move(self, vertical,
        limit) which returns a 3-element tuple containing the best move for the
        current player as a (row, column) tuple, its associated value, and the
        number of leaf nodes visited during the search. Recall that if the
        vertical parameter is True, then the current player intends to place a
        domino on squares (row, col) and (row + 1, col), and if the vertical
        parameter is False, then the current player intends to place a domino
        on squares (row, col) and (row, col + 1). Moves should be explored
        row-major order, described in further detail above, to ensure
        consistency.

        Your search should be a faithful implementation of the
        alpha-beta search given on page 154 of the course textbook (figure
        5.7), with the restriction that you should look no further than limit
        moves into the future. To evaluate a board, you should compute the
        number of moves available to the current player, then subtract the
        number of moves available to the opponent.

        One player always places the domino horizontally and the other always
        places it vertically.

        function ALPHA-BETA-SEARCH(game, state) returns an action
        player ←game.TO-MOVE(state)
        value, move←MAX-VALUE(game, state,−∞
        , +∞)
        return move

        function MAX-VALUE(game, state, α, β) returns a (utility, move) pair
        if game.IS-TERMINAL(state) then return
        game.UTILITY(state, player), null
        v← -∞
        for each a in game.ACTIONS(state) do
        v2, a2←MIN-VALUE(game, game.RESULT(state, a), α, β)
        if v2 > v then
        v, move←v2, a
        α ←MAX(α, v)
        if v ≥β then return v, move
        return v, move

        function MIN-VALUE(game, state, α, β) returns a
        (utility, move) pair
        if game.IS-TERMINAL(state) then return
        game.UTILITY(state, player), null
        v← +∞
        for each a in game.ACTIONS(state) do
        v2, a2←MAX-VALUE(game, game.RESULT(state, a), α, β)
        if v2 < v then
        v, move←v2, a
        β ←MIN(β, v)
        if v ≤α then return v, move
        return v, move
        """
        pass


############################################################
# Section 2: Feedback
############################################################


# Just an approximation is fine.
feedback_question_1 = """
I spent 4.5 hours on this assignment.
"""

feedback_question_2 = """
The most challenging part is implementing the get_best_move
method (alpha-beta search).
"""

feedback_question_3 = """
I enjoyed breaking down the game into separate methods and
seeing how it all comes together.
"""
